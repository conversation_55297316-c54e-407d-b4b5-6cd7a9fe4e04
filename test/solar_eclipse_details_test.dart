import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/models/astro_event.dart';
import 'package:astreal/services/astro_calendar_service.dart';

void main() {
  group('Solar Eclipse Details Tests', () {
    late AstroCalendarService service;

    setUp(() {
      service = AstroCalendarService();
    });

    testWidgets('測試SolarEclipseDetails類別功能', (WidgetTester tester) async {
      // 創建測試用的日食詳細信息
      final maximumTime = DateTime(2024, 4, 8, 18, 17, 0);
      final eclipseBeginTime = DateTime(2024, 4, 8, 17, 5, 0);
      final eclipseEndTime = DateTime(2024, 4, 8, 19, 29, 0);
      final totalityBeginTime = DateTime(2024, 4, 8, 18, 15, 0);
      final totalityEndTime = DateTime(2024, 4, 8, 18, 19, 0);

      final solarEclipseDetails = SolarEclipseDetails(
        maximumTime: maximumTime,
        eclipseBeginTime: eclipseBeginTime,
        eclipseEndTime: eclipseEndTime,
        totalityBeginTime: totalityBeginTime,
        totalityEndTime: totalityEndTime,
        eclipseType: EclipseType.solarTotal,
        magnitude: 1.024,
        isVisible: true,
        totalDuration: const Duration(hours: 2, minutes: 24),
        totalityDuration: const Duration(minutes: 4),
        description: '日全食，食分：102.4%',
      );

      // 測試基本屬性
      expect(solarEclipseDetails.maximumTime, equals(maximumTime));
      expect(solarEclipseDetails.eclipseType, equals(EclipseType.solarTotal));
      expect(solarEclipseDetails.magnitude, equals(1.024));
      expect(solarEclipseDetails.isVisible, isTrue);

      // 測試getAllPhases方法
      final phases = solarEclipseDetails.getAllPhases();
      expect(phases[SolarEclipsePhase.maximum], equals(maximumTime));
      expect(phases[SolarEclipsePhase.eclipseBegin], equals(eclipseBeginTime));
      expect(phases[SolarEclipsePhase.eclipseEnd], equals(eclipseEndTime));
      expect(phases[SolarEclipsePhase.totalityBegin], equals(totalityBeginTime));
      expect(phases[SolarEclipsePhase.totalityEnd], equals(totalityEndTime));

      print('✅ SolarEclipseDetails類別功能測試通過');
    });

    testWidgets('測試SolarEclipsePhase階段名稱', (WidgetTester tester) async {
      // 測試各個階段的中文名稱
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.maximum), equals('食甚'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.eclipseBegin), equals('初虧'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.eclipseEnd), equals('復圓'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.totalityBegin), equals('食既'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.totalityEnd), equals('生光'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.localNoon), equals('當地正午'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.centerLineBegin), equals('中心線開始'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.centerLineEnd), equals('中心線結束'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.annularToTotal), equals('環食變全食'));
      expect(SolarEclipseDetails.getPhaseName(SolarEclipsePhase.totalToAnnular), equals('全食變環食'));

      print('✅ SolarEclipsePhase階段名稱測試通過');
    });

    testWidgets('測試日食詳細信息解析', (WidgetTester tester) async {
      // 測試日食詳細信息的解析功能
      // 這裡我們模擬Swiss Ephemeris的回傳數據結構

      // 模擬times陣列（10個元素）
      final mockTimes = [
        2460408.26180556, // tret[0] 最大日食時間
        2460408.25000000, // tret[1] 當地正午時間
        2460408.21180556, // tret[2] 日食開始時間
        2460408.31180556, // tret[3] 日食結束時間
        2460408.26041667, // tret[4] 全食開始時間
        2460408.26319444, // tret[5] 全食結束時間
        0.0,              // tret[6] 中心線開始時間（未設定）
        0.0,              // tret[7] 中心線結束時間（未設定）
        0.0,              // tret[8] 環食變全食時間（未設定）
        0.0,              // tret[9] 全食變環食時間（未設定）
      ];

      // 驗證times陣列長度
      expect(mockTimes.length, equals(10));

      // 驗證時間順序的邏輯性
      expect(mockTimes[2] < mockTimes[0], isTrue); // 開始時間 < 最大時間
      expect(mockTimes[0] < mockTimes[3], isTrue); // 最大時間 < 結束時間
      expect(mockTimes[4] < mockTimes[5], isTrue); // 全食開始 < 全食結束

      print('✅ 日食詳細信息解析測試通過');
      print('模擬times陣列: $mockTimes');
    });

    testWidgets('測試日食持續時間計算', (WidgetTester tester) async {
      // 測試持續時間計算的準確性
      final eclipseBeginTime = DateTime(2024, 4, 8, 17, 5, 0);
      final eclipseEndTime = DateTime(2024, 4, 8, 19, 29, 0);
      final totalityBeginTime = DateTime(2024, 4, 8, 18, 15, 0);
      final totalityEndTime = DateTime(2024, 4, 8, 18, 19, 0);

      // 計算總持續時間
      final totalDuration = eclipseEndTime.difference(eclipseBeginTime);
      expect(totalDuration.inMinutes, equals(144)); // 2小時24分鐘

      // 計算全食持續時間
      final totalityDuration = totalityEndTime.difference(totalityBeginTime);
      expect(totalityDuration.inMinutes, equals(4)); // 4分鐘

      print('✅ 日食持續時間計算測試通過');
      print('總持續時間: ${totalDuration.inMinutes}分鐘');
      print('全食持續時間: ${totalityDuration.inMinutes}分鐘');
    });

    testWidgets('測試日食描述生成', (WidgetTester tester) async {
      // 測試詳細描述的生成
      final eclipseType = EclipseType.solarTotal;
      final magnitude = 1.024;
      final totalDuration = const Duration(hours: 2, minutes: 24);
      final totalityDuration = const Duration(minutes: 4);
      final latitude = 25.0;
      final longitude = 121.0;

      // 模擬描述生成邏輯
      final typeDescription = AstroEvent.getEclipseDisplayName(eclipseType);
      final magnitudePercent = (magnitude * 100).toStringAsFixed(1);
      
      String expectedDescription = '$typeDescription，食分：$magnitudePercent%';
      expectedDescription += '，總持續時間：${totalDuration.inMinutes}分${totalDuration.inSeconds % 60}秒';
      expectedDescription += '，全食持續時間：${totalityDuration.inMinutes}分${totalityDuration.inSeconds % 60}秒';
      
      final latStr = latitude >= 0 ? '北緯${latitude.toStringAsFixed(1)}°' : '南緯${(-latitude).toStringAsFixed(1)}°';
      final lonStr = longitude >= 0 ? '東經${longitude.toStringAsFixed(1)}°' : '西經${(-longitude).toStringAsFixed(1)}°';
      expectedDescription += '，觀測地點：$latStr $lonStr';

      // 驗證描述內容
      expect(expectedDescription.contains('日全蝕'), isTrue);
      expect(expectedDescription.contains('102.4%'), isTrue);
      expect(expectedDescription.contains('144分'), isTrue);
      expect(expectedDescription.contains('4分'), isTrue);
      expect(expectedDescription.contains('北緯25.0°'), isTrue);
      expect(expectedDescription.contains('東經121.0°'), isTrue);

      print('✅ 日食描述生成測試通過');
      print('生成的描述: $expectedDescription');
    });
  });
}
