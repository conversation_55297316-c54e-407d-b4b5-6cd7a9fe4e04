import 'package:astreal/services/equinox_solstice_service.dart';
import 'package:astreal/utils/julian_date_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sweph/sweph.dart';

/// 計算指定時間的太陽經度（測試用）
Future<double> calculateSunLongitude(
  DateTime dateTime,
  double latitude,
  double longitude,
) async {
  try {
    final julianDay = await JulianDateUtils.dateTimeToJulianDay(
      dateTime,
      latitude,
      longitude,
    );

    final result = Sweph.swe_calc_ut(
      julianDay,
      HeavenlyBody.SE_SUN,
      SwephFlag.SEFLG_SWIEPH | SwephFlag.SEFLG_SPEED,
    );

    double sunLongitude = result.longitude;
    while (sunLongitude < 0) {
      sunLongitude += 360;
    }
    while (sunLongitude >= 360) {
      sunLongitude -= 360;
    }

    return sunLongitude;
  } catch (e) {
    throw Exception('計算太陽經度失敗: $e');
  }
}

void main() {
  group('二分二至圖精度測試', () {
    late EquinoxSolsticeService service;

    setUpAll(() {
      // 初始化Flutter綁定，這是測試中必需的
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      service = EquinoxSolsticeService();
    });

    test('2024年春分精度測試 - 太陽經度應為0°', () async {
      final seasons = await service.calculateSeasonTimes(
        2024,
        latitude: 25.0330, // 台北緯度
        longitude: 121.5654, // 台北經度
      );

      final springEquinox = seasons.firstWhere(
        (season) => season.seasonType == SeasonType.springEquinox,
      );

      // 驗證春分時太陽經度接近0°
      final sunLongitude = await calculateSunLongitude(
        springEquinox.dateTime,
        25.0330,
        121.5654,
      );

      print('2024年春分時間: ${springEquinox.dateTime}');
      print('太陽經度: ${sunLongitude.toStringAsFixed(6)}°');
      print('與0°的差距: ${sunLongitude.toStringAsFixed(6)}°');

      // 精度要求：差距應小於0.001度（約3.6角秒）
      expect(sunLongitude.abs(), lessThan(0.001));
    });

    test('2024年夏至精度測試 - 太陽經度應為90°', () async {
      final seasons = await service.calculateSeasonTimes(
        2024,
        latitude: 25.0330,
        longitude: 121.5654,
      );

      final summerSolstice = seasons.firstWhere(
        (season) => season.seasonType == SeasonType.summerSolstice,
      );

      final sunLongitude = await calculateSunLongitude(
        summerSolstice.dateTime,
        25.0330,
        121.5654,
      );

      print('2024年夏至時間: ${summerSolstice.dateTime}');
      print('太陽經度: ${sunLongitude.toStringAsFixed(6)}°');
      print('與90°的差距: ${(sunLongitude - 90).abs().toStringAsFixed(6)}°');

      expect((sunLongitude - 90).abs(), lessThan(0.001));
    });

    test('2024年秋分精度測試 - 太陽經度應為180°', () async {
      final seasons = await service.calculateSeasonTimes(
        2024,
        latitude: 25.0330,
        longitude: 121.5654,
      );

      final autumnEquinox = seasons.firstWhere(
        (season) => season.seasonType == SeasonType.autumnEquinox,
      );

      final sunLongitude = await calculateSunLongitude(
        autumnEquinox.dateTime,
        25.0330,
        121.5654,
      );

      print('2024年秋分時間: ${autumnEquinox.dateTime}');
      print('太陽經度: ${sunLongitude.toStringAsFixed(6)}°');
      print('與180°的差距: ${(sunLongitude - 180).abs().toStringAsFixed(6)}°');

      expect((sunLongitude - 180).abs(), lessThan(0.001));
    });

    test('2024年冬至精度測試 - 太陽經度應為270°', () async {
      final seasons = await service.calculateSeasonTimes(
        2024,
        latitude: 25.0330,
        longitude: 121.5654,
      );

      final winterSolstice = seasons.firstWhere(
        (season) => season.seasonType == SeasonType.winterSolstice,
      );

      final sunLongitude = await calculateSunLongitude(
        winterSolstice.dateTime,
        25.0330,
        121.5654,
      );

      print('2024年冬至時間: ${winterSolstice.dateTime}');
      print('太陽經度: ${sunLongitude.toStringAsFixed(6)}°');
      print('與270°的差距: ${(sunLongitude - 270).abs().toStringAsFixed(6)}°');

      expect((sunLongitude - 270).abs(), lessThan(0.001));
    });

    test('多年份精度一致性測試', () async {
      final years = [2020, 2021, 2022, 2023, 2024, 2025];

      for (final year in years) {
        final seasons = await service.calculateSeasonTimes(
          year,
          latitude: 25.0330,
          longitude: 121.5654,
        );

        for (final season in seasons) {
          final sunLongitude = await calculateSunLongitude(
            season.dateTime,
            25.0330,
            121.5654,
          );

          final expectedLongitude = season.seasonType.solarLongitude;
          final difference = (sunLongitude - expectedLongitude).abs();

          // 處理跨越0度的情況
          final adjustedDifference = difference > 180 ? 360 - difference : difference;

          print('$year年${season.seasonType.displayName}: '
                '太陽經度=${sunLongitude.toStringAsFixed(6)}°, '
                '差距=${adjustedDifference.toStringAsFixed(6)}°');

          expect(adjustedDifference, lessThan(0.001),
                 reason: '$year年${season.seasonType.displayName}精度不符合要求');
        }
      }
    });
  });
}
