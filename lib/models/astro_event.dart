import 'package:flutter/material.dart';

/// 星象事件類型枚舉
enum AstroEventType {
  moonPhase,        // 月相
  seasonChange,     // 節氣變化
  planetAspect,     // 行星相位
  planetSignChange, // 行星換座
  planetRetrograde, // 行星逆行
  eclipse,          // 日月蝕
}

/// 月相類型枚舉
enum MoonPhaseType {
  newMoon,      // 新月
  waxingCrescent, // 娥眉月
  firstQuarter, // 上弦月
  waxingGibbous, // 盈凸月
  fullMoon,     // 滿月
  waningGibbous, // 虧凸月
  lastQuarter,  // 下弦月
  waningCrescent, // 殘月
}

/// 日月蝕類型枚舉
enum EclipseType {
  solarTotal,     // 日全蝕
  solarAnnular,   // 日環蝕
  solarPartial,   // 日偏蝕
  solarHybrid,    // 日混合蝕
  lunarTotal,     // 月全蝕
  lunarPartial,   // 月偏蝕
  lunarPenumbral, // 月半影蝕
}

/// 日食階段枚舉（對應Swiss Ephemeris times陣列）
enum SolarEclipsePhase {
  maximum,        // tret[0] 最大日食時間（食甚）
  localNoon,      // tret[1] 日食發生在當地正午的時間
  eclipseBegin,   // tret[2] 日食開始時間（初虧）
  eclipseEnd,     // tret[3] 日食結束時間（復圓）
  totalityBegin,  // tret[4] 全食開始時間（食既）
  totalityEnd,    // tret[5] 全食結束時間（生光）
  centerLineBegin,// tret[6] 中心線開始時間
  centerLineEnd,  // tret[7] 中心線結束時間
  annularToTotal, // tret[8] 日環食變成全食的時間
  totalToAnnular, // tret[9] 日全食再次變成日環食的時間
}

/// 日食詳細信息類別
class SolarEclipseDetails {
  final DateTime maximumTime;           // 食甚時間
  final DateTime? localNoonTime;        // 當地正午時間
  final DateTime? eclipseBeginTime;     // 初虧時間
  final DateTime? eclipseEndTime;       // 復圓時間
  final DateTime? totalityBeginTime;    // 食既時間
  final DateTime? totalityEndTime;      // 生光時間
  final DateTime? centerLineBeginTime;  // 中心線開始時間
  final DateTime? centerLineEndTime;    // 中心線結束時間
  final DateTime? annularToTotalTime;   // 環食變全食時間
  final DateTime? totalToAnnularTime;   // 全食變環食時間

  final EclipseType eclipseType;        // 日食類型
  final double magnitude;               // 食分
  final bool isVisible;                 // 可見性
  final Duration? totalDuration;        // 總持續時間
  final Duration? totalityDuration;     // 全食持續時間
  final String description;             // 描述

  SolarEclipseDetails({
    required this.maximumTime,
    this.localNoonTime,
    this.eclipseBeginTime,
    this.eclipseEndTime,
    this.totalityBeginTime,
    this.totalityEndTime,
    this.centerLineBeginTime,
    this.centerLineEndTime,
    this.annularToTotalTime,
    this.totalToAnnularTime,
    required this.eclipseType,
    required this.magnitude,
    required this.isVisible,
    this.totalDuration,
    this.totalityDuration,
    required this.description,
  });

  /// 獲取所有可用的時間階段
  Map<SolarEclipsePhase, DateTime?> getAllPhases() {
    return {
      SolarEclipsePhase.maximum: maximumTime,
      SolarEclipsePhase.localNoon: localNoonTime,
      SolarEclipsePhase.eclipseBegin: eclipseBeginTime,
      SolarEclipsePhase.eclipseEnd: eclipseEndTime,
      SolarEclipsePhase.totalityBegin: totalityBeginTime,
      SolarEclipsePhase.totalityEnd: totalityEndTime,
      SolarEclipsePhase.centerLineBegin: centerLineBeginTime,
      SolarEclipsePhase.centerLineEnd: centerLineEndTime,
      SolarEclipsePhase.annularToTotal: annularToTotalTime,
      SolarEclipsePhase.totalToAnnular: totalToAnnularTime,
    };
  }

  /// 獲取階段的中文名稱
  static String getPhaseName(SolarEclipsePhase phase) {
    switch (phase) {
      case SolarEclipsePhase.maximum:
        return '食甚';
      case SolarEclipsePhase.localNoon:
        return '當地正午';
      case SolarEclipsePhase.eclipseBegin:
        return '初虧';
      case SolarEclipsePhase.eclipseEnd:
        return '復圓';
      case SolarEclipsePhase.totalityBegin:
        return '食既';
      case SolarEclipsePhase.totalityEnd:
        return '生光';
      case SolarEclipsePhase.centerLineBegin:
        return '中心線開始';
      case SolarEclipsePhase.centerLineEnd:
        return '中心線結束';
      case SolarEclipsePhase.annularToTotal:
        return '環食變全食';
      case SolarEclipsePhase.totalToAnnular:
        return '全食變環食';
    }
  }
}

/// 星象事件模型
class AstroEvent {
  final String id;
  final String title;
  final String description;
  final DateTime dateTime;
  final AstroEventType type;
  final Color color;
  final IconData icon;
  final int importance; // 重要度 1-5
  final Map<String, dynamic>? additionalData;

  const AstroEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.dateTime,
    required this.type,
    required this.color,
    required this.icon,
    this.importance = 1,
    this.additionalData,
  });

  /// 獲取事件類型的顯示名稱
  String get typeDisplayName {
    switch (type) {
      case AstroEventType.moonPhase:
        return '月相';
      case AstroEventType.seasonChange:
        return '節氣';
      case AstroEventType.planetAspect:
        return '相位';
      case AstroEventType.planetSignChange:
        return '換座';
      case AstroEventType.planetRetrograde:
        return '逆行';
      case AstroEventType.eclipse:
        return '蝕相';
    }
  }

  /// 獲取重要度顏色
  Color get importanceColor {
    switch (importance) {
      case 5:
        return Colors.red;
      case 4:
        return Colors.orange;
      case 3:
        return Colors.amber;
      case 2:
        return Colors.blue;
      case 1:
      default:
        return Colors.grey;
    }
  }

  /// 獲取月相類型的顯示名稱
  static String getMoonPhaseDisplayName(MoonPhaseType phase) {
    switch (phase) {
      case MoonPhaseType.newMoon:
        return '新月';
      case MoonPhaseType.waxingCrescent:
        return '娥眉月';
      case MoonPhaseType.firstQuarter:
        return '上弦月';
      case MoonPhaseType.waxingGibbous:
        return '盈凸月';
      case MoonPhaseType.fullMoon:
        return '滿月';
      case MoonPhaseType.waningGibbous:
        return '虧凸月';
      case MoonPhaseType.lastQuarter:
        return '下弦月';
      case MoonPhaseType.waningCrescent:
        return '殘月';
    }
  }

  /// 獲取月相圖標
  static IconData getMoonPhaseIcon(MoonPhaseType phase) {
    switch (phase) {
      case MoonPhaseType.newMoon:
        return Icons.brightness_1;
      case MoonPhaseType.waxingCrescent:
        return Icons.brightness_2;
      case MoonPhaseType.firstQuarter:
        return Icons.brightness_3;
      case MoonPhaseType.waxingGibbous:
        return Icons.brightness_4;
      case MoonPhaseType.fullMoon:
        return Icons.brightness_7;
      case MoonPhaseType.waningGibbous:
        return Icons.brightness_4;
      case MoonPhaseType.lastQuarter:
        return Icons.brightness_3;
      case MoonPhaseType.waningCrescent:
        return Icons.brightness_2;
    }
  }

  /// 獲取日月蝕顯示名稱
  static String getEclipseDisplayName(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return '日全蝕';
      case EclipseType.solarAnnular:
        return '日環蝕';
      case EclipseType.solarPartial:
        return '日偏蝕';
      case EclipseType.solarHybrid:
        return '日混合蝕';
      case EclipseType.lunarTotal:
        return '月全蝕';
      case EclipseType.lunarPartial:
        return '月偏蝕';
      case EclipseType.lunarPenumbral:
        return '月半影蝕';
    }
  }

  /// 獲取日月蝕圖標
  static IconData getEclipseIcon(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
      case EclipseType.solarAnnular:
      case EclipseType.solarPartial:
      case EclipseType.solarHybrid:
        return Icons.wb_sunny_outlined; // 日蝕圖標
      case EclipseType.lunarTotal:
      case EclipseType.lunarPartial:
      case EclipseType.lunarPenumbral:
        return Icons.nightlight_round; // 月蝕圖標
    }
  }

  /// 獲取日月蝕顏色
  static Color getEclipseColor(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return Colors.black87; // 日全蝕 - 黑色
      case EclipseType.solarAnnular:
        return Colors.orange; // 日環蝕 - 橙色
      case EclipseType.solarPartial:
        return Colors.amber; // 日偏蝕 - 琥珀色
      case EclipseType.solarHybrid:
        return Colors.deepOrange; // 日混合蝕 - 深橙色
      case EclipseType.lunarTotal:
        return Colors.red.shade800; // 月全蝕 - 深紅色
      case EclipseType.lunarPartial:
        return Colors.red.shade400; // 月偏蝕 - 紅色
      case EclipseType.lunarPenumbral:
        return Colors.grey.shade600; // 月半影蝕 - 灰色
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AstroEvent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AstroEvent{id: $id, title: $title, dateTime: $dateTime, type: $type}';
  }
}
